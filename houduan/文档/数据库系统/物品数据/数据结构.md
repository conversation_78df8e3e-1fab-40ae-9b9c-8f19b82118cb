# 物品数据表结构

本文档包含物品相关数据表的详细结构信息。

## item_name 表结构

| 字段名 | 数据类型 | 允许空值 | 主键 | 自增 | 注释 |
|--------|----------|----------|------|------|------|
| # | TEXT | YES | NO | NO |  |
| ID | TEXT | YES | NO | NO |  |
| zhognwenming | TEXT | YES | NO | NO |  |
| zhognwenjieshao | TEXT | YES | NO | NO |  |

## wupin_huizong 表结构

| 字段名 | 数据类型 | 允许空值 | 主键 | 自增 | 注释 |
|--------|----------|----------|------|------|------|
| # | TEXT | YES | NO | NO |  |
| wupin_id | TEXT | YES | NO | NO |  |
| wupin_mingcheng | TEXT | YES | NO | NO |  |
| zhongwenming | TEXT | YES | NO | NO |  |
| wanzhengxing_fenshu | TEXT | YES | NO | NO |  |
| leixing | TEXT | YES | NO | NO |  |
| zileixing | TEXT | YES | NO | NO |  |
| zhongliang | TEXT | YES | NO | NO |  |
| guaiwu_shuliang | TEXT | YES | NO | NO |  |
| shangjia_shuliang | TEXT | YES | NO | NO |  |
| chongwu_shuliang | TEXT | YES | NO | NO |  |
| xiangzi_shuliang | TEXT | YES | NO | NO |  |
| lishi_jilu_shuliang | TEXT | YES | NO | NO |  |
| jichuxinxi_yaml | TEXT | YES | NO | NO |  |
| guaiwulaiyuan_yaml | TEXT | YES | NO | NO |  |
| shoumaishangjia_yaml | TEXT | YES | NO | NO |  |
| chongwu_yaml | TEXT | YES | NO | NO |  |
| laiyuanxiangzi_yaml | TEXT | YES | NO | NO |  |
| lishibiandong_yaml | TEXT | YES | NO | NO |  |
| you_jichuxinxi | TEXT | YES | NO | NO |  |
| you_guaiwulaiyuan | TEXT | YES | NO | NO |  |
| you_shoumaishangjia | TEXT | YES | NO | NO |  |
| you_chongwu | TEXT | YES | NO | NO |  |
| you_laiyuanxiangzi | TEXT | YES | NO | NO |  |
| you_lishibiandong | TEXT | YES | NO | NO |  |
| leiming | TEXT | YES | NO | NO | 物品类名（英文名称） |

## 物品列表项数据结构

物品查询API返回的列表项包含以下字段：

```json
{
  "wupin_id": 909,
  "wupin_mingcheng": "杰勒比结晶",
  "leixing": "Etc.",
  "zileixing": "-",
  "leiming": "Jellopy"
}
```

### 字段说明

- `wupin_id`: 物品ID（整数）
- `wupin_mingcheng`: 物品中文名称（优先使用item_name表的zhognwenming，没有则使用wupin_huizong表的zhongwenming）
- `leixing`: 物品类型（可选字符串）
- `zileixing`: 物品子类型（可选字符串）
- `leiming`: 物品类名/英文名称（可选字符串）

---

*生成时间: 2025-07-28 12:45:52*
*更新时间: 2025-07-29 07:10:00 - 添加leiming字段和物品列表项数据结构说明*
