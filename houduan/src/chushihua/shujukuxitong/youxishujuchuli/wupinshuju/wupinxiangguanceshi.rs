#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::wupinshuju_liebiao_chaxun::wupin_liebiao_chaxun_guanli;
use super::wupin_mingzi_chaxun::{wupin_mingzi_chaxun_guanli, mingzi_chaxun_tiaojian, mingzi_chaxun_leixing};
use super::wupin_redis_kongzhi::wupin_redis_kongzhi;
use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use super::wupinjiegouti::{fenye_canshu, wupin_liebiao_jieguo};
use anyhow::Result;

/// 物品列表查询相关测试类
pub struct wupin_xiangguanceshi;

impl wupin_xiangguanceshi {
    /// 测试物品名字查询功能
    pub async fn ceshi_wupin_mingzi_chaxun(
        mysql_lianjie: mysql_lianjie_guanli,
        redis_lianjie: Option<redis_lianjie_guanli>
    ) -> Result<()> {
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "开始测试物品名字查询功能...",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        // 创建查询管理器
        let mingzi_chaxun_guanli = if let Some(ref redis) = redis_lianjie {
            wupin_mingzi_chaxun_guanli::new_with_redis(mysql_lianjie, redis.clone())
        } else {
            wupin_mingzi_chaxun_guanli::new(mysql_lianjie)
        };

        // 创建分页参数
        let fenye_canshu = fenye_canshu::new(1, 10);

        // 测试1: 模糊名字查询 - "杰勒"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试1: 模糊名字查询 - '杰勒'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        let mohu_chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_mohu(
            "杰勒".to_string(),
            fenye_canshu.clone()
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&mohu_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("模糊查询'杰勒' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("模糊查询'杰勒'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("模糊查询'杰勒'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
            }
        }

        // 测试2: 精确名字查询 - "杰勒比结晶"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试2: 精确名字查询 - '杰勒比结晶'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        let jingque_chaxun_tiaojian = mingzi_chaxun_tiaojian::mingzi_jingque(
            "杰勒比结晶".to_string(),
            fenye_canshu.clone()
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&jingque_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("精确查询'杰勒比结晶' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("精确查询'杰勒比结晶'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("精确查询'杰勒比结晶'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
            }
        }

        // 测试3: 类名精确查询 - "Jellopy"
        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "测试3: 类名精确查询 - 'Jellopy'",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        let leiming_chaxun_tiaojian = mingzi_chaxun_tiaojian::leiming_jingque(
            "Jellopy".to_string(),
            fenye_canshu.clone()
        );

        match mingzi_chaxun_guanli.tongyong_mingzi_chaxun(&leiming_chaxun_tiaojian).await {
            Ok(jieguo) => {
                // 显示数据来源
                let laiyuan = jieguo.shuju_laiyuan.as_deref().unwrap_or("未知");
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("类名查询'Jellopy' - 数据来源: {}", laiyuan),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );

                // 显示原始JSON内容
                match serde_json::to_string_pretty(&jieguo) {
                    Ok(json_str) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("类名查询'Jellopy'原始返回内容:\n{}", json_str),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("序列化结果失败: {}", e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                    }
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("类名查询'Jellopy'出错: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
            }
        }

        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
            "物品名字查询功能测试完成！",
            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
        );

        // 测试完成后清除缓存
        if let Some(redis) = redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis);
            if let Err(e) = redis_kongzhi.qingchu_ceshi_huancun().await {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("清除测试缓存失败: {}", e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
            }
        }

        Ok(())
    }
}