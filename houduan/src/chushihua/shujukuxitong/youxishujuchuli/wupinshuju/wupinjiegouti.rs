#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// item_name表基础信息结构体
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct wupin_jiben_xinxi {
    /// 物品ID
    pub id: String,
    /// 中文名称
    pub zhognwenming: Option<String>,
    /// 中文介绍
    pub zhognwenjieshao: Option<String>,
}

/// wupin_huizong表汇总信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_huizong_xinxi {
    /// 物品ID
    pub wupin_id: Option<String>,
    /// 物品名称
    pub wupin_mingcheng: Option<String>,
    /// 中文名
    pub zhongwenming: Option<String>,
    /// 完整性分数
    pub wanzhengxing_fenshu: Option<String>,
    /// 类型
    pub leixing: Option<String>,
    /// 子类型
    pub zileixing: Option<String>,
    /// 重量
    pub zhongliang: Option<String>,
    /// 怪物数量
    pub guaiwu_shuliang: Option<String>,
    /// 商家数量
    pub shangjia_shuliang: Option<String>,
    /// 宠物数量
    pub chongwu_shuliang: Option<String>,
    /// 箱子数量
    pub xiangzi_shuliang: Option<String>,
    /// 历史记录数量
    pub lishi_jilu_shuliang: Option<String>,
    /// 基础信息yaml
    pub jichuxinxi_yaml: Option<String>,
    /// 怪物来源yaml
    pub guaiwulaiyuan_yaml: Option<String>,
    /// 售卖商家yaml
    pub shoumaishangjia_yaml: Option<String>,
    /// 宠物yaml
    pub chongwu_yaml: Option<String>,
    /// 来源箱子yaml
    pub laiyuanxiangzi_yaml: Option<String>,
    /// 历史变动yaml
    pub lishibiandong_yaml: Option<String>,
    /// 有基础信息
    pub you_jichuxinxi: Option<String>,
    /// 有怪物来源
    pub you_guaiwulaiyuan: Option<String>,
    /// 有售卖商家
    pub you_shoumaishangjia: Option<String>,
    /// 有宠物
    pub you_chongwu: Option<String>,
    /// 有来源箱子
    pub you_laiyuanxiangzi: Option<String>,
    /// 有历史变动
    pub you_lishibiandong: Option<String>,
}

/// 完整物品信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_wanzheng_xinxi {
    /// 物品ID
    pub id: String,
    /// 基础信息
    pub jiben_xinxi: Option<wupin_jiben_xinxi>,
    /// 汇总信息
    pub huizong_xinxi: Option<wupin_huizong_xinxi>,
}

/// 物品查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 完整物品信息（当查询全部信息时）
    pub wanzheng_xinxi: Option<wupin_wanzheng_xinxi>,
    /// 物品数据映射（当查询指定字段时）
    pub wupin_shuju: Option<HashMap<String, String>>,
}

/// 支持的字段映射
pub struct wupin_ziduan_yingshe;

impl wupin_ziduan_yingshe {
    /// 获取item_name表支持的字段
    pub fn huoqu_jiben_biao_ziduan() -> Vec<String> {
        vec![
            "ID".to_string(),
            "zhognwenming".to_string(),
            "zhognwenjieshao".to_string(),
        ]
    }

    /// 获取wupin_huizong表支持的字段
    pub fn huoqu_huizong_biao_ziduan() -> Vec<String> {
        vec![
            "wupin_id".to_string(),
            "wupin_mingcheng".to_string(),
            "zhongwenming".to_string(),
            "wanzhengxing_fenshu".to_string(),
            "leixing".to_string(),
            "zileixing".to_string(),
            "zhongliang".to_string(),
            "guaiwu_shuliang".to_string(),
            "shangjia_shuliang".to_string(),
            "chongwu_shuliang".to_string(),
            "xiangzi_shuliang".to_string(),
            "lishi_jilu_shuliang".to_string(),
            "jichuxinxi_yaml".to_string(),
            "guaiwulaiyuan_yaml".to_string(),
            "shoumaishangjia_yaml".to_string(),
            "chongwu_yaml".to_string(),
            "laiyuanxiangzi_yaml".to_string(),
            "lishibiandong_yaml".to_string(),
            "you_jichuxinxi".to_string(),
            "you_guaiwulaiyuan".to_string(),
            "you_shoumaishangjia".to_string(),
            "you_chongwu".to_string(),
            "you_laiyuanxiangzi".to_string(),
            "you_lishibiandong".to_string(),
        ]
    }

    /// 获取所有支持的字段映射
    pub fn huoqu_suoyou_ziduan_yingshe() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert("item_name".to_string(), Self::huoqu_jiben_biao_ziduan());
        ziduan_map.insert("wupin_huizong".to_string(), Self::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 检查字段是否有效
    pub fn jiancha_ziduan_youxiao(ziduan_ming: &str) -> bool {
        let suoyou_ziduan = Self::huoqu_suoyou_ziduan_yingshe();
        for (_biao_ming, ziduan_liebiao) in suoyou_ziduan {
            if ziduan_liebiao.contains(&ziduan_ming.to_string()) {
                return true;
            }
        }
        false
    }
}