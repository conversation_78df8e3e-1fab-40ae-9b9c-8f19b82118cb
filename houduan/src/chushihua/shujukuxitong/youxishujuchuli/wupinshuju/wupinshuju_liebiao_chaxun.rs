#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use super::wupin_rizhi_kongzhi::wupin_zifuchuan_changliangguanli;
use super::wupin_redis_kongzhi::wupin_redis_kongzhi;
use serde::{Deserialize, Serialize};
use sqlx::Row;

/// 物品列表项信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_liebiao_xiang {
    /// 物品ID
    pub wupin_id: i32,
    /// 物品名称（优先item_name表，没有则用汇总表）
    pub wupin_mingcheng: String,
    /// 物品类型
    pub leixing: Option<String>,
    /// 物品子类型
    pub zileixing: Option<String>,
}

/// 分页参数结构体
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct fenye_canshu {
    /// 页码（从1开始）
    pub yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
}

impl fenye_canshu {
    /// 创建新的分页参数
    pub fn new(yema: u32, meiye_shuliang: u32) -> Self {
        Self {
            yema: if yema == 0 { 1 } else { yema },
            meiye_shuliang: if meiye_shuliang == 0 { 10 } else { meiye_shuliang },
        }
    }

    /// 计算偏移量
    pub fn jisuan_pianyi(&self) -> u32 {
        (self.yema - 1) * self.meiye_shuliang
    }

    /// 计算总页数
    pub fn jisuan_zong_yeshu(&self, zong_shuliang: u64) -> u32 {
        if zong_shuliang == 0 {
            return 1;
        }
        ((zong_shuliang as f64) / (self.meiye_shuliang as f64)).ceil() as u32
    }
}

/// 物品列表查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct wupin_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 物品列表
    pub wupin_liebiao: Vec<wupin_liebiao_xiang>,
    /// 当前页码
    pub dangqian_yema: u32,
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 总数量
    pub zong_shuliang: u64,
    /// 总页数
    pub zong_yeshu: u32,
}

impl wupin_liebiao_jieguo {
    /// 创建成功的查询结果
    pub fn chenggong(
        wupin_liebiao: Vec<wupin_liebiao_xiang>,
        fenye_canshu: &fenye_canshu,
        zong_shuliang: u64,
    ) -> Self {
        let zong_yeshu = fenye_canshu.jisuan_zong_yeshu(zong_shuliang);
        Self {
            chenggong: true,
            cuowu_xinxi: None,
            wupin_liebiao,
            dangqian_yema: fenye_canshu.yema,
            meiye_shuliang: fenye_canshu.meiye_shuliang,
            zong_shuliang,
            zong_yeshu,
        }
    }

    /// 创建失败的查询结果
    pub fn shibai(cuowu_xinxi: String) -> Self {
        Self {
            chenggong: false,
            cuowu_xinxi: Some(cuowu_xinxi),
            wupin_liebiao: Vec::new(),
            dangqian_yema: 1,
            meiye_shuliang: 10,
            zong_shuliang: 0,
            zong_yeshu: 1,
        }
    }
}

/// 物品列表查询管理器
pub struct wupin_liebiao_chaxun_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_lianjie: Option<redis_lianjie_guanli>,
}

impl wupin_liebiao_chaxun_guanli {
    /// 创建新的物品列表查询管理实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: None,
        }
    }

    /// 创建带Redis连接的物品列表查询管理实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_lianjie: Some(redis_lianjie),
        }
    }

    /// 获取物品列表（分页）
    pub async fn huoqu_wupin_liebiao(&self, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis) = &self.redis_lianjie {
            let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_liebiao_fenye(
                fenye_canshu.yema,
                fenye_canshu.meiye_shuliang
            );

            if let Ok(Some(huancun_shuju)) = redis.huoqu(&huancun_jian).await {
                if let Ok(liebiao_jieguo) = serde_json::from_str::<wupin_liebiao_jieguo>(&huancun_shuju) {
                    return Ok(liebiao_jieguo);
                }
            }
        }

        // 从数据库查询
        match self.chaxun_shujuku_liebiao(fenye_canshu).await {
            Ok(liebiao_jieguo) => {
                // 缓存到Redis
                if let Some(redis) = &self.redis_lianjie {
                    let huancun_jian = wupin_zifuchuan_changliangguanli::shengcheng_redis_jian_liebiao_fenye(
                        fenye_canshu.yema,
                        fenye_canshu.meiye_shuliang
                    );

                    if let Ok(json_shuju) = serde_json::to_string(&liebiao_jieguo) {
                        let _ = redis.shezhi(&huancun_jian, &json_shuju).await;
                        let _ = redis.shezhi_guoqi(&huancun_jian, wupin_zifuchuan_changliangguanli::liebiao_huancun_shijian as i64).await;
                    }
                }

                Ok(liebiao_jieguo)
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_liebiao_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Ok(wupin_liebiao_jieguo::shibai(cuowu_xinxi))
            }
        }
    }

    /// 从数据库查询物品列表
    async fn chaxun_shujuku_liebiao(&self, fenye_canshu: &fenye_canshu) -> anyhow::Result<wupin_liebiao_jieguo> {
        // 先查询总数
        let zong_shuliang = self.chaxun_wupin_zongshu().await?;

        // 查询分页数据
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_liebiao_fenye;
        let pianyi = fenye_canshu.jisuan_pianyi();



        match sqlx::query(sql)
            .bind(fenye_canshu.meiye_shuliang as i64)
            .bind(pianyi as i64)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut wupin_liebiao = Vec::new();

                for row in rows {
                    if let (Ok(wupin_id), Ok(wupin_mingcheng)) = (
                        row.try_get::<i32, _>("wupin_id"),
                        row.try_get::<String, _>("wupin_mingcheng")
                    ) {
                        let leixing = row.try_get::<String, _>("leixing").ok();
                        let zileixing = row.try_get::<String, _>("zileixing").ok();

                        wupin_liebiao.push(wupin_liebiao_xiang {
                            wupin_id,
                            wupin_mingcheng,
                            leixing,
                            zileixing,
                        });
                    }
                }

                Ok(wupin_liebiao_jieguo::chenggong(wupin_liebiao, fenye_canshu, zong_shuliang))
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_liebiao_shibai(&e.to_string());
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 查询物品总数
    async fn chaxun_wupin_zongshu(&self) -> anyhow::Result<u64> {
        let sql = wupin_zifuchuan_changliangguanli::sql_chaxun_liebiao_zongshu;

        match sqlx::query(sql)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                match row.try_get::<i64, _>("zong_shuliang") {
                    Ok(count) => Ok(count as u64),
                    Err(e) => {
                        let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &cuowu_xinxi,
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                        );
                        Err(anyhow::anyhow!(cuowu_xinxi))
                    }
                }
            }
            Err(e) => {
                let cuowu_xinxi = wupin_zifuchuan_changliangguanli::shengcheng_cuowu_chaxun_zongshu_shibai(&e.to_string());
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &cuowu_xinxi,
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi
                );
                Err(anyhow::anyhow!(cuowu_xinxi))
            }
        }
    }

    /// 获取物品列表缓存统计信息
    pub async fn huoqu_liebiao_huancun_tongji(&self) -> anyhow::Result<String> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis.clone());
            redis_kongzhi.huoqu_liebiao_huancun_tongji().await
        } else {
            Ok(wupin_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }

    /// 清除物品列表缓存
    pub async fn qingchu_liebiao_huancun(&self) -> anyhow::Result<()> {
        if let Some(redis) = &self.redis_lianjie {
            let redis_kongzhi = wupin_redis_kongzhi::new(redis.clone());
            redis_kongzhi.qingchu_liebiao_huancun().await
        } else {
            Ok(())
        }
    }
}